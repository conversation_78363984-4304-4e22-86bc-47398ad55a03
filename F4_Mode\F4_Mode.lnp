--cpu=Cortex-M4.fp.sp
"f4_mode\startup_stm32f407xx.o"
"f4_mode\main.o"
"f4_mode\gpio.o"
"f4_mode\dma.o"
"f4_mode\i2c.o"
"f4_mode\tim.o"
"f4_mode\usart.o"
"f4_mode\stm32f4xx_it.o"
"f4_mode\stm32f4xx_hal_msp.o"
"f4_mode\stm32f4xx_hal_i2c.o"
"f4_mode\stm32f4xx_hal_i2c_ex.o"
"f4_mode\stm32f4xx_hal_rcc.o"
"f4_mode\stm32f4xx_hal_rcc_ex.o"
"f4_mode\stm32f4xx_hal_flash.o"
"f4_mode\stm32f4xx_hal_flash_ex.o"
"f4_mode\stm32f4xx_hal_flash_ramfunc.o"
"f4_mode\stm32f4xx_hal_gpio.o"
"f4_mode\stm32f4xx_hal_dma_ex.o"
"f4_mode\stm32f4xx_hal_dma.o"
"f4_mode\stm32f4xx_hal_pwr.o"
"f4_mode\stm32f4xx_hal_pwr_ex.o"
"f4_mode\stm32f4xx_hal_cortex.o"
"f4_mode\stm32f4xx_hal.o"
"f4_mode\stm32f4xx_hal_exti.o"
"f4_mode\stm32f4xx_hal_tim.o"
"f4_mode\stm32f4xx_hal_tim_ex.o"
"f4_mode\stm32f4xx_hal_uart.o"
"f4_mode\system_stm32f4xx.o"
"f4_mode\ebtn.o"
"f4_mode\ebtn_driver.o"
"f4_mode\emm_v5.o"
"f4_mode\motor_driver.o"
"f4_mode\encoder_driver.o"
"f4_mode\hardware_iic.o"
"f4_mode\hwt101_driver.o"
"f4_mode\led_driver.o"
"f4_mode\oled.o"
"f4_mode\pid.o"
"f4_mode\ringbuffer.o"
"f4_mode\uart_driver.o"
"f4_mode\scheduler.o"
"f4_mode\key_app.o"
"f4_mode\led_app.o"
"f4_mode\oled_app.o"
"f4_mode\pid_app.o"
"f4_mode\uart_app.o"
"f4_mode\step_motor_app.o"
--strict --scatter "F4_Mode\F4_Mode.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "F4_Mode.map" -o F4_Mode\F4_Mode.axf